---
interface Props {
  title: string;
  description: string;
  tags: string[];
  slug?: string;
  image?: string;
  isFeatured?: boolean;
  technologies?: string[];
  github?: string;
  live?: string;
  featured?: boolean;
  problem?: string;
  publishDate?: Date;
}

const { 
  title, 
  description, 
  tags, 
  slug, 
  image, 
  isFeatured = false,
  technologies = [],
  github,
  live,
  featured = false,
  problem,
  publishDate
} = Astro.props;

// Truncate long technology names for better display
const formatTag = (tag: string) => {
  if (tag.length > 12) {
    return tag.substring(0, 10) + '..';
  }
  return tag;
};

// Limit to maximum 6 tags for clean presentation
const displayTags = tags.slice(0, 6);

// Extract metrics from description for display
const extractMetrics = (desc?: string) => {
  if (!desc || typeof desc !== 'string') return [];
  
  const metrics = [];
  // Performance improvements
  const perfMatch = desc.match(/(\d+)%\s+(reduction|improvement|increase)/gi);
  if (perfMatch) {
    metrics.push(perfMatch[0]);
  }
  // User metrics
  const userMatch = desc.match(/(\d+)%\s+(satisfaction|uptime|accuracy)/gi);
  if (userMatch) {
    metrics.push(userMatch[0]);
  }
  // Scale metrics
  const scaleMatch = desc.match(/(\d+)x\s+(traffic|capacity|increase)/gi);
  if (scaleMatch) {
    metrics.push(scaleMatch[0]);
  }
  return metrics.slice(0, 2); // Show max 2 metrics
};

const displayMetrics = extractMetrics(description);

// Format date for display
const formatDate = (date?: Date) => {
  if (!date) return '';
  return new Intl.DateTimeFormat('en-US', { 
    year: 'numeric', 
    month: 'short' 
  }).format(date);
};


// Dynamic card classes for bento grid support
const cardClasses = `
  portfolio-card group relative flex flex-col h-full
  bg-white/80 dark:bg-secondary-800/80 
  border border-secondary-200/40 dark:border-secondary-700/40
  rounded-2xl overflow-hidden
  transition-all duration-300 ease-in-out
  hover:border-primary-400/60 dark:hover:border-primary-500/60
  hover:shadow-xl hover:shadow-secondary-900/10 dark:hover:shadow-black/30
  hover:-translate-y-2 motion-safe:hover:-translate-y-2
  focus-within:ring-2 focus-within:ring-primary-500/50 focus-within:ring-offset-2
  ${isFeatured ? 'md:col-span-2 lg:col-span-2' : ''}
`;
---

<article class={cardClasses} role="listitem">

  <!-- Project image with modern aspect ratio -->
  <div class="relative aspect-video bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/40 dark:to-accent-950/20 overflow-hidden">
    {image ? (
      <img
        src={image}
        alt={`${title} preview`}
        class="w-full h-full object-cover transition-transform duration-500 ease-out group-hover:scale-105"
        loading="lazy"
      />
    ) : (
      <div class="w-full h-full flex items-center justify-center relative overflow-hidden">
        {/* Enhanced gradient background based on project type */}
        <div class={`absolute inset-0 ${
          tags.includes('AI/ML') ? 'bg-gradient-to-br from-purple-500/20 via-blue-500/20 to-cyan-500/20' :
          tags.includes('Mobile') ? 'bg-gradient-to-br from-green-500/20 via-emerald-500/20 to-teal-500/20' :
          tags.includes('Backend') || tags.includes('Microservices') ? 'bg-gradient-to-br from-orange-500/20 via-red-500/20 to-pink-500/20' :
          tags.includes('DevOps') || tags.includes('Infrastructure') || tags.includes('Cloud') ? 'bg-gradient-to-br from-blue-600/20 via-indigo-500/20 to-purple-500/20' :
          tags.includes('Blockchain') || tags.includes('DeFi') || tags.includes('Web3') ? 'bg-gradient-to-br from-yellow-500/20 via-orange-500/20 to-red-500/20' :
          tags.includes('Analytics') || tags.includes('Data') || tags.includes('Real-time') ? 'bg-gradient-to-br from-cyan-500/20 via-blue-500/20 to-indigo-500/20' :
          tags.includes('IoT') || tags.includes('Industrial') ? 'bg-gradient-to-br from-slate-500/20 via-gray-500/20 to-zinc-500/20' :
          'bg-gradient-to-br from-primary-500/20 via-accent-500/20 to-secondary-500/20'
        }`}></div>

        {/* Decorative pattern overlay */}
        <div class="absolute inset-0 opacity-10">
          <div class="absolute top-4 left-4 w-8 h-8 border border-current rounded-full"></div>
          <div class="absolute top-8 right-8 w-4 h-4 bg-current rounded-full"></div>
          <div class="absolute bottom-6 left-8 w-6 h-6 border border-current rotate-45"></div>
          <div class="absolute bottom-4 right-4 w-3 h-3 bg-current rounded-full"></div>
        </div>

        {/* Main icon based on project type */}
        <div class="relative z-10">
          {tags.includes('AI/ML') ? (
            <div class="flex flex-col items-center space-y-2">
              <svg class="w-20 h-20 text-purple-500 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
                <div class="w-2 h-2 bg-cyan-400 rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
              </div>
            </div>
          ) : tags.includes('Mobile') ? (
            <div class="flex flex-col items-center space-y-2">
              <svg class="w-20 h-20 text-green-500 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-1 h-4 bg-green-400 rounded-full"></div>
                <div class="w-1 h-6 bg-emerald-400 rounded-full"></div>
                <div class="w-1 h-5 bg-teal-400 rounded-full"></div>
                <div class="w-1 h-7 bg-green-400 rounded-full"></div>
              </div>
            </div>
          ) : tags.includes('Backend') || tags.includes('Microservices') ? (
            <div class="flex flex-col items-center space-y-2">
              <svg class="w-20 h-20 text-orange-500 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"/>
              </svg>
              <div class="grid grid-cols-3 gap-1">
                <div class="w-2 h-2 bg-orange-400 rounded-sm"></div>
                <div class="w-2 h-2 bg-red-400 rounded-sm"></div>
                <div class="w-2 h-2 bg-pink-400 rounded-sm"></div>
              </div>
            </div>
          ) : tags.includes('DevOps') || tags.includes('Infrastructure') || tags.includes('Cloud') ? (
            <div class="flex flex-col items-center space-y-2">
              <svg class="w-20 h-20 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M2.25 15a4.5 4.5 0 004.5 4.5H18a3.75 3.75 0 001.332-7.257 3 3 0 00-3.758-3.848 5.25 5.25 0 00-10.233 2.33A4.502 4.502 0 002.25 15z"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-3 h-1 bg-blue-400 rounded-full"></div>
                <div class="w-2 h-1 bg-indigo-400 rounded-full"></div>
                <div class="w-4 h-1 bg-purple-400 rounded-full"></div>
              </div>
            </div>
          ) : tags.includes('Blockchain') || tags.includes('DeFi') || tags.includes('Web3') ? (
            <div class="flex flex-col items-center space-y-2">
              <svg class="w-20 h-20 text-yellow-500 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13.5 10.5V6.75a4.5 4.5 0 119 0v3.75M3.75 21.75h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 9l1.5 1.5L15 6"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-yellow-400 rounded-full transform rotate-45"></div>
                <div class="w-2 h-2 bg-orange-400 rounded-full transform rotate-45"></div>
                <div class="w-2 h-2 bg-red-400 rounded-full transform rotate-45"></div>
              </div>
            </div>
          ) : tags.includes('Analytics') || tags.includes('Data') || tags.includes('Real-time') ? (
            <div class="flex flex-col items-center space-y-2">
              <svg class="w-20 h-20 text-cyan-500 dark:text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-1 h-3 bg-cyan-400 rounded-full"></div>
                <div class="w-1 h-5 bg-blue-400 rounded-full"></div>
                <div class="w-1 h-4 bg-indigo-400 rounded-full"></div>
                <div class="w-1 h-6 bg-cyan-400 rounded-full"></div>
              </div>
            </div>
          ) : tags.includes('IoT') || tags.includes('Industrial') ? (
            <div class="flex flex-col items-center space-y-2">
              <svg class="w-20 h-20 text-slate-600 dark:text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8.288 15.038a5.25 5.25 0 017.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.53 18.22l-.53.53-.53-.53a.75.75 0 011.06 0z"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-slate-400 rounded-full animate-ping"></div>
                <div class="w-2 h-2 bg-gray-400 rounded-full animate-ping" style="animation-delay: 0.3s"></div>
                <div class="w-2 h-2 bg-zinc-400 rounded-full animate-ping" style="animation-delay: 0.6s"></div>
              </div>
            </div>
          ) : (
            <div class="flex flex-col items-center space-y-2">
              <svg class="w-20 h-20 text-primary-500 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-primary-400 rounded-full"></div>
                <div class="w-2 h-2 bg-accent-400 rounded-full"></div>
                <div class="w-2 h-2 bg-secondary-400 rounded-full"></div>
              </div>
            </div>
          )}
        </div>
      </div>
    )}
    <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
  </div>

  <!-- Card content with perfect alignment using CSS Grid -->
  <div class="portfolio-content p-6 grid grid-rows-[auto_1fr] h-full">
    
    <!-- 1. Title Section (Fixed Height) -->
    <header class="title-section mb-3">
      <h3 class="text-xl font-bold text-secondary-900 dark:text-secondary-100 line-clamp-2 leading-tight group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300 min-h-[3.5rem]">
        {title}
      </h3>
    </header>

    <!-- 2. Description Section (Flexible Height) -->
    <div class="description-section flex flex-col justify-between">
      <p class="text-secondary-600 dark:text-secondary-400 leading-relaxed text-sm line-clamp-4">
        {description}
      </p>
      
      <!-- 3. Tech Stack Section (NO spacing) -->
      <div class="text-xs text-secondary-500 dark:text-secondary-400">
        {displayTags.slice(0, 3).join(' • ')}
        {displayTags.length > 3 && ` • +${displayTags.length - 3} more`}
      </div>
    </div>
    
  </div>

</article>

<style>
  /* Line clamping utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Modern 2025 card animations */
  .portfolio-card {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Reduced motion support for accessibility */
  @media (prefers-reduced-motion: reduce) {
    .portfolio-card,
    .portfolio-card *,
    .tag {
      animation: none !important;
      transition: none !important;
    }
    
    .portfolio-card:hover {
      transform: none;
    }
  }

  /* Enhanced accessibility focus styles */
  .portfolio-card:focus-within {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  /* Content section alignment */
  .portfolio-content {
    min-height: 240px; /* Ensures consistent card content height */
  }

  .title-section h3 {
    display: flex;
    align-items: flex-start;
    min-height: 3rem; /* Fixed space for titles */
  }

  .description-section {
    min-height: 4rem; /* Minimum space for descriptions */
  }

  /* Responsive height adjustments */
  @media (max-width: 768px) {
    .portfolio-card {
      min-height: 300px;
    }
    
    .portfolio-content {
      min-height: 200px;
    }
    
    .title-section h3 {
      min-height: 2.5rem;
    }
    
    .description-section {
      min-height: 3rem;
    }
  }
</style>