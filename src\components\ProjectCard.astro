---
interface Props {
  title: string;
  description: string;
  tags: string[];
  slug?: string;
  image?: string;
  isFeatured?: boolean;
  technologies?: string[];
  github?: string;
  live?: string;
  featured?: boolean;
  problem?: string;
  publishDate?: Date;
}

const { 
  title, 
  description, 
  tags, 
  slug, 
  image, 
  isFeatured = false,
  technologies = [],
  github,
  live,
  featured = false,
  problem,
  publishDate
} = Astro.props;

// Truncate long technology names for better display
const formatTag = (tag: string) => {
  if (tag.length > 12) {
    return tag.substring(0, 10) + '..';
  }
  return tag;
};

// Limit to maximum 6 tags for clean presentation
const displayTags = tags.slice(0, 6);

// Extract metrics from description for display
const extractMetrics = (desc?: string) => {
  if (!desc || typeof desc !== 'string') return [];
  
  const metrics = [];
  // Performance improvements
  const perfMatch = desc.match(/(\d+)%\s+(reduction|improvement|increase)/gi);
  if (perfMatch) {
    metrics.push(perfMatch[0]);
  }
  // User metrics
  const userMatch = desc.match(/(\d+)%\s+(satisfaction|uptime|accuracy)/gi);
  if (userMatch) {
    metrics.push(userMatch[0]);
  }
  // Scale metrics
  const scaleMatch = desc.match(/(\d+)x\s+(traffic|capacity|increase)/gi);
  if (scaleMatch) {
    metrics.push(scaleMatch[0]);
  }
  return metrics.slice(0, 2); // Show max 2 metrics
};

const displayMetrics = extractMetrics(description);

// Format date for display
const formatDate = (date?: Date) => {
  if (!date) return '';
  return new Intl.DateTimeFormat('en-US', { 
    year: 'numeric', 
    month: 'short' 
  }).format(date);
};


// Dynamic card classes for bento grid support
const cardClasses = `
  portfolio-card group relative flex flex-col h-full
  bg-white/80 dark:bg-secondary-800/80 
  border border-secondary-200/40 dark:border-secondary-700/40
  rounded-2xl overflow-hidden
  transition-all duration-300 ease-in-out
  hover:border-primary-400/60 dark:hover:border-primary-500/60
  hover:shadow-xl hover:shadow-secondary-900/10 dark:hover:shadow-black/30
  hover:-translate-y-2 motion-safe:hover:-translate-y-2
  focus-within:ring-2 focus-within:ring-primary-500/50 focus-within:ring-offset-2
  ${isFeatured ? 'md:col-span-2 lg:col-span-2' : ''}
`;
---

<article class={cardClasses} role="listitem">

  <!-- Project image with modern aspect ratio -->
  <div class="relative aspect-video bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/40 dark:to-accent-950/20 overflow-hidden">
    {image ? (
      <img
        src={image}
        alt={`${title} preview`}
        class="w-full h-full object-cover transition-transform duration-500 ease-out group-hover:scale-105"
        loading="lazy"
      />
    ) : (
      <div class="w-full h-full flex items-center justify-center">
        {tags.includes('AI/ML') ? (
          <svg class="w-16 h-16 text-primary-400 dark:text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
          </svg>
        ) : tags.includes('Mobile') ? (
          <svg class="w-16 h-16 text-primary-400 dark:text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"/>
          </svg>
        ) : tags.includes('Backend') ? (
          <svg class="w-16 h-16 text-primary-400 dark:text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"/>
          </svg>
        ) : (
          <svg class="w-16 h-16 text-primary-400 dark:text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"/>
          </svg>
        )}
      </div>
    )}
    <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
  </div>

  <!-- Card content with perfect alignment using CSS Grid -->
  <div class="portfolio-content p-6 grid grid-rows-[auto_1fr] h-full">
    
    <!-- 1. Title Section (Fixed Height) -->
    <header class="title-section mb-3">
      <h3 class="text-xl font-bold text-secondary-900 dark:text-secondary-100 line-clamp-2 leading-tight group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300 min-h-[3.5rem]">
        {title}
      </h3>
    </header>

    <!-- 2. Description Section (Flexible Height) -->
    <div class="description-section flex flex-col justify-between">
      <p class="text-secondary-600 dark:text-secondary-400 leading-relaxed text-sm line-clamp-4">
        {description}
      </p>
      
      <!-- 3. Tech Stack Section (NO spacing) -->
      <div class="text-xs text-secondary-500 dark:text-secondary-400">
        {displayTags.slice(0, 3).join(' • ')}
        {displayTags.length > 3 && ` • +${displayTags.length - 3} more`}
      </div>
    </div>
    
  </div>

</article>

<style>
  /* Line clamping utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Modern 2025 card animations */
  .portfolio-card {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Reduced motion support for accessibility */
  @media (prefers-reduced-motion: reduce) {
    .portfolio-card,
    .portfolio-card *,
    .tag {
      animation: none !important;
      transition: none !important;
    }
    
    .portfolio-card:hover {
      transform: none;
    }
  }

  /* Enhanced accessibility focus styles */
  .portfolio-card:focus-within {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  /* Content section alignment */
  .portfolio-content {
    min-height: 240px; /* Ensures consistent card content height */
  }

  .title-section h3 {
    display: flex;
    align-items: flex-start;
    min-height: 3rem; /* Fixed space for titles */
  }

  .description-section {
    min-height: 4rem; /* Minimum space for descriptions */
  }

  /* Responsive height adjustments */
  @media (max-width: 768px) {
    .portfolio-card {
      min-height: 300px;
    }
    
    .portfolio-content {
      min-height: 200px;
    }
    
    .title-section h3 {
      min-height: 2.5rem;
    }
    
    .description-section {
      min-height: 3rem;
    }
  }
</style>