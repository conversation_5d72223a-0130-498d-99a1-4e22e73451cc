---
interface Props {
  title: string;
  description: string;
  tags: string[];
  slug?: string;
  image?: string;
  isFeatured?: boolean;
  technologies?: string[];
  github?: string;
  live?: string;
  featured?: boolean;
  problem?: string;
  publishDate?: Date;
}

const { 
  title, 
  description, 
  tags, 
  slug, 
  image, 
  isFeatured = false,
  technologies = [],
  github,
  live,
  featured = false,
  problem,
  publishDate
} = Astro.props;

// Truncate long technology names for better display
const formatTag = (tag: string) => {
  if (tag.length > 12) {
    return tag.substring(0, 10) + '..';
  }
  return tag;
};

// Limit to maximum 6 tags for clean presentation
const displayTags = tags.slice(0, 6);




// Dynamic card classes for bento grid support
const cardClasses = `
  portfolio-card group relative flex flex-col h-full
  bg-white/80 dark:bg-secondary-800/80 
  border border-secondary-200/40 dark:border-secondary-700/40
  rounded-2xl overflow-hidden
  transition-all duration-300 ease-in-out
  hover:border-primary-400/60 dark:hover:border-primary-500/60
  hover:shadow-xl hover:shadow-secondary-900/10 dark:hover:shadow-black/30
  hover:-translate-y-2 motion-safe:hover:-translate-y-2
  focus-within:ring-2 focus-within:ring-primary-500/50 focus-within:ring-offset-2
  ${isFeatured ? 'md:col-span-2 lg:col-span-2' : ''}
`;
---

<article class={cardClasses} role="listitem">

  <!-- Project image with modern aspect ratio -->
  <div class="relative aspect-video bg-gradient-to-br from-primary-50 to-accent-50 dark:from-primary-950/40 dark:to-accent-950/20 overflow-hidden">
    {image ? (
      <img
        src={image}
        alt={`${title} preview`}
        class="w-full h-full object-cover transition-transform duration-500 ease-out group-hover:scale-105"
        loading="lazy"
      />
    ) : (
      <div class="w-full h-full flex items-center justify-center relative overflow-hidden">
        {/* Dynamic gradient background based on project type */}
        {tags.includes('AI/ML') ? (
          <div class="absolute inset-0 bg-gradient-to-br from-purple-500/30 via-blue-500/20 to-cyan-500/30">
            <div class="absolute inset-0 bg-gradient-to-t from-purple-900/20 to-transparent"></div>
          </div>
        ) : tags.includes('Mobile') ? (
          <div class="absolute inset-0 bg-gradient-to-br from-green-500/30 via-emerald-500/20 to-teal-500/30">
            <div class="absolute inset-0 bg-gradient-to-t from-green-900/20 to-transparent"></div>
          </div>
        ) : tags.includes('Backend') || tags.includes('Microservices') ? (
          <div class="absolute inset-0 bg-gradient-to-br from-orange-500/30 via-red-500/20 to-pink-500/30">
            <div class="absolute inset-0 bg-gradient-to-t from-orange-900/20 to-transparent"></div>
          </div>
        ) : tags.includes('DevOps') || tags.includes('Infrastructure') || tags.includes('Cloud') ? (
          <div class="absolute inset-0 bg-gradient-to-br from-blue-600/30 via-indigo-500/20 to-purple-500/30">
            <div class="absolute inset-0 bg-gradient-to-t from-blue-900/20 to-transparent"></div>
          </div>
        ) : tags.includes('Blockchain') || tags.includes('DeFi') || tags.includes('Web3') ? (
          <div class="absolute inset-0 bg-gradient-to-br from-yellow-500/30 via-orange-500/20 to-red-500/30">
            <div class="absolute inset-0 bg-gradient-to-t from-yellow-900/20 to-transparent"></div>
          </div>
        ) : tags.includes('Analytics') || tags.includes('Data') || tags.includes('Real-time') ? (
          <div class="absolute inset-0 bg-gradient-to-br from-cyan-500/30 via-blue-500/20 to-indigo-500/30">
            <div class="absolute inset-0 bg-gradient-to-t from-cyan-900/20 to-transparent"></div>
          </div>
        ) : tags.includes('IoT') || tags.includes('Industrial') ? (
          <div class="absolute inset-0 bg-gradient-to-br from-slate-500/30 via-gray-500/20 to-zinc-500/30">
            <div class="absolute inset-0 bg-gradient-to-t from-slate-900/20 to-transparent"></div>
          </div>
        ) : (
          <div class="absolute inset-0 bg-gradient-to-br from-primary-500/30 via-accent-500/20 to-secondary-500/30">
            <div class="absolute inset-0 bg-gradient-to-t from-primary-900/20 to-transparent"></div>
          </div>
        )}

        {/* Subtle decorative elements */}
        <div class="absolute inset-0 opacity-20">
          <div class="absolute top-6 left-6 w-6 h-6 border border-white/40 rounded-full"></div>
          <div class="absolute top-12 right-8 w-3 h-3 bg-white/40 rounded-full"></div>
          <div class="absolute bottom-8 left-12 w-4 h-4 border border-white/40 rotate-45"></div>
          <div class="absolute bottom-6 right-6 w-2 h-2 bg-white/40 rounded-full"></div>
        </div>

        {/* Main icon with enhanced styling */}
        <div class="relative z-10 flex flex-col items-center space-y-3">
          {tags.includes('AI/ML') ? (
            <>
              <svg class="w-24 h-24 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-white/80 rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-white/60 rounded-full animate-pulse" style="animation-delay: 0.2s"></div>
                <div class="w-2 h-2 bg-white/80 rounded-full animate-pulse" style="animation-delay: 0.4s"></div>
              </div>
            </>
          ) : tags.includes('Mobile') ? (
            <>
              <svg class="w-24 h-24 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-1 h-4 bg-white/70 rounded-full"></div>
                <div class="w-1 h-6 bg-white/90 rounded-full"></div>
                <div class="w-1 h-5 bg-white/60 rounded-full"></div>
                <div class="w-1 h-7 bg-white/80 rounded-full"></div>
              </div>
            </>
          ) : tags.includes('Backend') || tags.includes('Microservices') ? (
            <>
              <svg class="w-24 h-24 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"/>
              </svg>
              <div class="grid grid-cols-3 gap-1">
                <div class="w-2 h-2 bg-white/70 rounded-sm"></div>
                <div class="w-2 h-2 bg-white/90 rounded-sm"></div>
                <div class="w-2 h-2 bg-white/60 rounded-sm"></div>
              </div>
            </>
          ) : tags.includes('DevOps') || tags.includes('Infrastructure') || tags.includes('Cloud') ? (
            <>
              <svg class="w-24 h-24 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M2.25 15a4.5 4.5 0 004.5 4.5H18a3.75 3.75 0 001.332-7.257 3 3 0 00-3.758-3.848 5.25 5.25 0 00-10.233 2.33A4.502 4.502 0 002.25 15z"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-3 h-1 bg-white/80 rounded-full"></div>
                <div class="w-2 h-1 bg-white/60 rounded-full"></div>
                <div class="w-4 h-1 bg-white/90 rounded-full"></div>
              </div>
            </>
          ) : tags.includes('Blockchain') || tags.includes('DeFi') || tags.includes('Web3') ? (
            <>
              <svg class="w-24 h-24 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13.5 10.5V6.75a4.5 4.5 0 119 0v3.75M3.75 21.75h16.5M4.5 3h15M5.25 3v18m13.5-18v18M9 9l1.5 1.5L15 6"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-white/70 rounded-full transform rotate-45"></div>
                <div class="w-2 h-2 bg-white/90 rounded-full transform rotate-45"></div>
                <div class="w-2 h-2 bg-white/60 rounded-full transform rotate-45"></div>
              </div>
            </>
          ) : tags.includes('Analytics') || tags.includes('Data') || tags.includes('Real-time') ? (
            <>
              <svg class="w-24 h-24 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-1 h-3 bg-white/70 rounded-full"></div>
                <div class="w-1 h-5 bg-white/90 rounded-full"></div>
                <div class="w-1 h-4 bg-white/60 rounded-full"></div>
                <div class="w-1 h-6 bg-white/80 rounded-full"></div>
              </div>
            </>
          ) : tags.includes('IoT') || tags.includes('Industrial') ? (
            <>
              <svg class="w-24 h-24 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8.288 15.038a5.25 5.25 0 017.424 0M5.106 11.856c3.807-3.808 9.98-3.808 13.788 0M1.924 8.674c5.565-5.565 14.587-5.565 20.152 0M12.53 18.22l-.53.53-.53-.53a.75.75 0 011.06 0z"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-white/70 rounded-full animate-ping"></div>
                <div class="w-2 h-2 bg-white/50 rounded-full animate-ping" style="animation-delay: 0.3s"></div>
                <div class="w-2 h-2 bg-white/80 rounded-full animate-ping" style="animation-delay: 0.6s"></div>
              </div>
            </>
          ) : (
            <>
              <svg class="w-24 h-24 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z"/>
              </svg>
              <div class="flex space-x-1">
                <div class="w-2 h-2 bg-white/70 rounded-full"></div>
                <div class="w-2 h-2 bg-white/90 rounded-full"></div>
                <div class="w-2 h-2 bg-white/60 rounded-full"></div>
              </div>
            </>
          )}
        </div>
      </div>
    )}
    <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
  </div>

  <!-- Card content with perfect alignment using CSS Grid -->
  <div class="portfolio-content p-6 grid grid-rows-[auto_1fr] h-full">
    
    <!-- 1. Title Section (Fixed Height) -->
    <header class="title-section mb-3">
      <h3 class="text-xl font-bold text-secondary-900 dark:text-secondary-100 line-clamp-2 leading-tight group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300 min-h-[3.5rem]">
        {title}
      </h3>
    </header>

    <!-- 2. Description Section (Flexible Height) -->
    <div class="description-section flex flex-col justify-between">
      <p class="text-secondary-600 dark:text-secondary-400 leading-relaxed text-sm line-clamp-4">
        {description}
      </p>
      
      <!-- 3. Tech Stack Section (NO spacing) -->
      <div class="text-xs text-secondary-500 dark:text-secondary-400">
        {displayTags.slice(0, 3).join(' • ')}
        {displayTags.length > 3 && ` • +${displayTags.length - 3} more`}
      </div>
    </div>
    
  </div>

</article>

<style>
  /* Line clamping utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Modern 2025 card animations */
  .portfolio-card {
    animation: fadeInUp 0.6s ease-out forwards;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Reduced motion support for accessibility */
  @media (prefers-reduced-motion: reduce) {
    .portfolio-card,
    .portfolio-card *,
    .tag {
      animation: none !important;
      transition: none !important;
    }
    
    .portfolio-card:hover {
      transform: none;
    }
  }

  /* Enhanced accessibility focus styles */
  .portfolio-card:focus-within {
    outline: 2px solid transparent;
    outline-offset: 2px;
  }

  /* Content section alignment */
  .portfolio-content {
    min-height: 240px; /* Ensures consistent card content height */
  }

  .title-section h3 {
    display: flex;
    align-items: flex-start;
    min-height: 3rem; /* Fixed space for titles */
  }

  .description-section {
    min-height: 4rem; /* Minimum space for descriptions */
  }

  /* Responsive height adjustments */
  @media (max-width: 768px) {
    .portfolio-card {
      min-height: 300px;
    }
    
    .portfolio-content {
      min-height: 200px;
    }
    
    .title-section h3 {
      min-height: 2.5rem;
    }
    
    .description-section {
      min-height: 3rem;
    }
  }
</style>